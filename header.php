<!DOCTYPE html>
<html <?php language_attributes(); ?> class="light">
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <?php if(is_singular('houses')) { ?>
        <meta property="og:image" content="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'hero-bg')); ?>" />
    <?php } ?>
	<?php wp_head(); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<?php $header_fields = get_field('header', 'options'); ?>
<header class="vm-header">
    <div class="vm-grid vm-grid--align-center">
        <div class="vm-col-6 vm-col-lg-4 vm-col-xl-6">
            <?php 
            if( is_singular('houses') ) :
            ?>
                <div class="vm-header__logo vm-header__logo--alt" data-aos="fade-right">
                    <a href="/">
                        <svg width="193" height="23" viewBox="0 0 193 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M53.0886 1.83252H58.4122V2.7076H54.0932V5.55689H57.819V6.40825H54.0932V10.1801H53.0886V1.83252Z" fill="white"/>
                            <path d="M61.2046 1.83252H62.6732L65.8558 10.1827H64.7668L63.9626 8.01872H59.9152L59.111 10.1827H58.022L61.2046 1.83252ZM63.6541 7.16736L61.9376 2.50728L60.2211 7.16736H63.6541Z" fill="white"/>
                            <path d="M66.1697 7.50948H67.2349C67.3404 8.78784 68.1578 9.52059 69.4472 9.52059C70.6548 9.52059 71.4696 8.88272 71.4696 7.9233C71.4696 7.1062 71.0556 6.76355 69.7188 6.44462L68.8196 6.23112C67.1638 5.84102 66.4887 5.17944 66.4887 4.03023C66.4887 2.64644 67.6726 1.68701 69.3628 1.68701C71.1031 1.68701 72.2738 2.69125 72.403 4.26482H71.3377C71.2534 3.15251 70.5203 2.49093 69.3628 2.49093C68.287 2.49093 67.5513 3.08135 67.5513 3.94589C67.5513 4.70236 67.9416 5.03447 69.1835 5.32968L70.1301 5.55372C71.8572 5.96754 72.5322 6.6054 72.5322 7.81259C72.5322 9.30182 71.2665 10.3192 69.4314 10.3192C67.554 10.3245 66.3226 9.25964 66.1697 7.50948Z" fill="white"/>
                            <path d="M75.5671 2.7076H73.0701V1.83252H79.0687V2.7076H76.5717V10.1827H75.5671V2.7076Z" fill="white"/>
                            <path d="M80.1471 1.83252H81.1517V10.1827H80.1471V1.83252Z" fill="white"/>
                            <path d="M82.5994 6.04456C82.5994 3.44304 84.1366 1.69287 86.4332 1.69287C88.2447 1.69287 89.6052 2.78146 89.9348 4.5079H88.8696C88.6454 3.26644 87.7226 2.49679 86.4332 2.49679C84.7536 2.49679 83.6646 3.88058 83.6646 6.04456C83.6646 8.18482 84.7404 9.53435 86.4701 9.53435C87.9968 9.53435 89.1201 8.45895 89.1201 6.99081V6.70615H87.4167V5.84161H90.0561V10.1828H89.099V8.82269H89.0515C88.4846 9.79266 87.4906 10.3251 86.2829 10.3251C84.0891 10.3251 82.5994 8.59864 82.5994 6.04456Z" fill="white"/>
                            <path d="M91.7937 1.83252H92.8115V5.34603H97.0224V1.83252H98.0402V10.1827H97.0224V6.22112H92.8115V10.1827H91.7937V1.83252Z" fill="white"/>
                            <path d="M99.9387 1.83252H105.262V2.7076H100.943V5.39348H104.669V6.24484H100.943V9.30762H105.262V10.1827H99.9387V1.83252Z" fill="white"/>
                            <path d="M108.606 2.7076H106.109V1.83252H112.107V2.7076H109.61V10.1827H108.606V2.7076Z" fill="white"/>
                            <path d="M112.564 7.50948H113.629C113.734 8.78784 114.552 9.52059 115.841 9.52059C117.049 9.52059 117.863 8.88272 117.863 7.9233C117.863 7.1062 117.45 6.76355 116.113 6.44462L115.214 6.23112C113.558 5.84102 112.883 5.17944 112.883 4.03023C112.883 2.64644 114.067 1.68701 115.757 1.68701C117.497 1.68701 118.668 2.69125 118.797 4.26482H117.732C117.65 3.15251 116.914 2.49093 115.757 2.49093C114.681 2.49093 113.945 3.08135 113.945 3.94589C113.945 4.70236 114.335 5.03447 115.577 5.32968L116.524 5.55372C118.251 5.96754 118.926 6.6054 118.926 7.81259C118.926 9.30182 117.66 10.3192 115.825 10.3192C113.948 10.3245 112.719 9.25964 112.564 7.50948Z" fill="white"/>
                            <path d="M120.395 1.83252H125.718V2.7076H121.399V5.55689H125.125V6.40825H121.399V10.1801H120.395V1.83252Z" fill="white"/>
                            <path d="M126.296 6.00746C126.296 3.41647 127.833 1.69003 130.14 1.69003C132.447 1.69003 133.985 3.41647 133.985 6.00746C133.985 8.59844 132.447 10.3249 130.14 10.3249C127.833 10.3249 126.296 8.59844 126.296 6.00746ZM132.922 6.00746C132.922 3.87774 131.833 2.49395 130.14 2.49395C128.448 2.49395 127.359 3.87774 127.359 6.00746C127.359 8.13718 128.448 9.52097 130.14 9.52097C131.833 9.52097 132.922 8.13718 132.922 6.00746ZM128.603 0.000488281H129.668V1.12333H128.603V0.000488281ZM130.615 0.000488281H131.68V1.12333H130.615V0.000488281Z" fill="white"/>
                            <path d="M135.456 1.83252H138.805C140.26 1.83252 141.241 2.67334 141.241 3.93852C141.241 4.97966 140.579 5.70186 139.477 5.9259V5.97335C140.329 6.00762 140.861 6.57695 140.861 7.43885V8.90435C140.861 9.55539 140.933 9.80316 141.217 10.1827H140.165C139.928 9.91122 139.857 9.62656 139.857 8.90435V7.62599C139.857 6.79835 139.443 6.4188 138.544 6.4188H136.461V10.1801H135.456V1.83252ZM138.628 5.54635C139.609 5.54635 140.202 5.01392 140.202 4.1283C140.202 3.24003 139.611 2.71024 138.628 2.71024H136.463V5.54899H138.628V5.54635Z" fill="white"/>
                            <path d="M142.768 1.83252H144.424L147.016 9.38933L149.608 1.83252H151.263V10.1827H150.269V2.75505L147.701 10.1827H146.33L143.762 2.75505V10.1827H142.768V1.83252Z" fill="white"/>
                            <path d="M153.167 1.83252H158.493V2.7076H154.174V5.39348H157.9V6.24484H154.174V9.30762H158.493V10.1827H153.17V1.83252H153.167Z" fill="white"/>
                            <path d="M159.733 1.83252H162.491C164.835 1.83252 166.359 3.51152 166.359 6.00761C166.359 8.50371 164.832 10.1827 162.491 10.1827H159.733V1.83252ZM162.491 9.30762C164.218 9.30762 165.296 8.04244 165.296 6.00761C165.296 3.97278 164.22 2.7076 162.491 2.7076H160.74V9.30762H162.491Z" fill="white"/>
                            <path d="M167.806 1.83252H168.811V9.30762H173.13V10.1827H167.806V1.83252Z" fill="white"/>
                            <path d="M174.203 1.83252H175.211V10.1827H174.203V1.83252Z" fill="white"/>
                            <path d="M177.085 1.83252H178.575L182.551 9.18901V1.83252H183.545V10.1827H182.056L178.079 2.82621V10.1827H177.085V1.83252Z" fill="white"/>
                            <path d="M185.017 6.04456C185.017 3.44304 186.554 1.69287 188.851 1.69287C190.662 1.69287 192.023 2.78146 192.352 4.5079H191.287C191.063 3.26644 190.14 2.49679 188.851 2.49679C187.171 2.49679 186.082 3.88058 186.082 6.04456C186.082 8.18482 187.158 9.53435 188.887 9.53435C190.414 9.53435 191.537 8.45895 191.537 6.99081V6.70615H189.834V5.84161H192.474V10.1828H191.516V8.82269H191.469C190.902 9.79266 189.908 10.3251 188.7 10.3251C186.509 10.3251 185.017 8.59864 185.017 6.04456Z" fill="white"/>
                            <path d="M53.0886 14.3365H56.0101C57.4525 14.3365 58.3885 15.1299 58.3885 16.3714C58.3885 17.3071 57.8559 18.0267 56.9093 18.2402V18.2876C58.0457 18.4642 58.7076 19.2681 58.7076 20.3699C58.7076 21.7668 57.6555 22.6894 56.0576 22.6894H53.0886V14.3365ZM55.8678 17.8975C56.7907 17.8975 57.3338 17.402 57.3338 16.548C57.3338 15.6966 56.7907 15.1985 55.8678 15.1985H54.0932V17.8949H55.8678V17.8975ZM55.9864 21.8248C57.0385 21.8248 57.6423 21.2581 57.6423 20.2882C57.6423 19.3182 57.0148 18.7515 55.9732 18.7515H54.0906V21.8275H55.9864V21.8248Z" fill="white"/>
                            <path d="M60.1604 14.3365H65.4841V15.2116H61.165V17.8975H64.8908V18.7489H61.165V21.8117H65.4841V22.6867H60.1604V14.3365Z" fill="white"/>
                            <path d="M66.726 14.3365H70.0747C71.5302 14.3365 72.511 15.1774 72.511 16.4425C72.511 17.4837 71.8492 18.2059 70.747 18.4299V18.4774C71.5987 18.5116 72.1313 19.081 72.1313 19.9429V21.4084C72.1313 22.0594 72.2025 22.3072 72.4873 22.6867H71.4352C71.1979 22.4153 71.1267 22.1306 71.1267 21.4084V20.13C71.1267 19.3024 70.7128 18.9228 69.8136 18.9228H67.7306V22.6841H66.726V14.3365ZM69.898 18.0504C70.8789 18.0504 71.4721 17.518 71.4721 16.6323C71.4721 15.7467 70.8815 15.2143 69.898 15.2143H67.7332V18.053H69.898V18.0504Z" fill="white"/>
                            <path d="M73.5183 18.5486C73.5183 15.9471 75.0556 14.1969 77.3522 14.1969C79.1636 14.1969 80.5242 15.2855 80.8538 17.0119H79.7886C79.5644 15.7705 78.6416 15.0008 77.3522 15.0008C75.6726 15.0008 74.5836 16.3846 74.5836 18.5486C74.5836 20.6889 75.6594 22.0384 77.3891 22.0384C78.9158 22.0384 80.039 20.963 80.039 19.4948V19.2102H78.3357V18.3456H80.9725V22.6868H80.0153V21.3267H79.9678C79.4009 22.2967 78.4069 22.8291 77.1992 22.8291C75.0081 22.8291 73.5183 21.1027 73.5183 18.5486Z" fill="white"/>
                            <path d="M82.7101 14.3365H88.0337V15.2116H83.7147V17.8975H87.4405V18.7489H83.7147V21.8117H88.0337V22.6867H82.7101V14.3365Z" fill="white"/>
                            <path d="M91.3798 15.2116H88.8828V14.3365H94.8815V15.2116H92.3844V22.6867H91.3798V15.2116Z" fill="white"/>
                            <path d="M95.3375 20.0144H96.4028C96.5083 21.2927 97.3257 22.0255 98.615 22.0255C99.8227 22.0255 100.637 21.3876 100.637 20.4282C100.637 19.6111 100.223 19.2684 98.8866 18.9495L97.9875 18.736C96.3316 18.3459 95.6566 17.6843 95.6566 16.5351C95.6566 15.1513 96.8405 14.1919 98.5306 14.1919C100.271 14.1919 101.442 15.1961 101.571 16.7697H100.506C100.424 15.6574 99.6882 14.9958 98.5306 14.9958C97.4548 14.9958 96.7218 15.5862 96.7218 16.4508C96.7218 17.2072 97.1121 17.5394 98.354 17.8346L99.3006 18.0586C101.028 18.4724 101.703 19.1103 101.703 20.3175C101.703 21.8067 100.437 22.8241 98.6018 22.8241C96.7218 22.8294 95.4905 21.7645 95.3375 20.0144Z" fill="white"/>
                            <path d="M103.169 14.3365H106.517C107.973 14.3365 108.954 15.1774 108.954 16.4425C108.954 17.4837 108.292 18.2059 107.19 18.4299V18.4774C108.042 18.5116 108.574 19.081 108.574 19.9429V21.4084C108.574 22.0594 108.645 22.3072 108.93 22.6867H107.878C107.641 22.4153 107.57 22.1306 107.57 21.4084V20.13C107.57 19.3024 107.156 18.9228 106.256 18.9228H104.173V22.6841H103.166V14.3365H103.169ZM106.338 18.0504C107.319 18.0504 107.912 17.518 107.912 16.6323C107.912 15.7467 107.322 15.2143 106.338 15.2143H104.173V18.053H106.338V18.0504Z" fill="white"/>
                            <path d="M109.961 18.5114C109.961 15.9204 111.498 14.194 113.805 14.194C116.113 14.194 117.65 15.9204 117.65 18.5114C117.65 21.1024 116.113 22.8288 113.805 22.8288C111.498 22.8288 109.961 21.1024 109.961 18.5114ZM116.585 18.5114C116.585 16.3817 115.496 14.9979 113.803 14.9979C112.11 14.9979 111.021 16.3817 111.021 18.5114C111.021 20.6411 112.11 22.0249 113.803 22.0249C115.498 22.0249 116.585 20.6411 116.585 18.5114Z" fill="white"/>
                            <path d="M118.612 21.5033H119.725V22.6868H118.612V21.5033Z" fill="white"/>
                            <path d="M120.827 20.0144H121.892C121.998 21.2927 122.815 22.0255 124.105 22.0255C125.312 22.0255 126.127 21.3876 126.127 20.4282C126.127 19.6111 125.713 19.2684 124.376 18.9495L123.477 18.736C121.821 18.3459 121.146 17.6843 121.146 16.5351C121.146 15.1513 122.33 14.1919 124.02 14.1919C125.761 14.1919 126.931 15.1961 127.061 16.7697H125.995C125.913 15.6574 125.178 14.9958 124.02 14.9958C122.945 14.9958 122.209 15.5862 122.209 16.4508C122.209 17.2072 122.599 17.5394 123.841 17.8346L124.788 18.0586C126.515 18.4724 127.19 19.1103 127.19 20.3175C127.19 21.8067 125.924 22.8241 124.089 22.8241C122.211 22.8294 120.983 21.7645 120.827 20.0144Z" fill="white"/>
                            <path d="M128.658 14.3365H133.982V15.2116H129.663V17.8975H133.389V18.7489H129.663V21.8117H133.982V22.6867H128.658V14.3365Z" fill="white"/>
                            <path d="M23.839 0.630249C9.5899 0.630249 0 9.33891 0 22.705H4.99666H42.6787H43.9233H47.6754C47.678 9.33891 38.0881 0.630249 23.839 0.630249Z" fill="white"/>
                        </svg>
                    </a>
                </div>
            <?php
            else :
            ?>
                <div class="vm-header__logo" data-aos="fade-right">
                    <a href="/">
                        <svg width="321" height="26" viewBox="0 0 321 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0.4198H8.62702C12.888 0.4198 15.6473 2.75655 15.6473 6.42065C15.6473 9.17722 14.0745 11.3041 11.2813 11.9338V12.0727C14.6338 12.5975 16.5897 14.9682 16.5897 18.2125C16.5897 22.3303 13.4813 25.0498 8.76607 25.0498H0V0.4198ZM8.20679 10.9213C10.9321 10.9213 12.5388 9.45504 12.5388 6.94542C12.5388 4.43272 10.9321 2.96954 8.20679 2.96954H2.9694V10.9244H8.20679V10.9213ZM8.55595 22.5032C11.6644 22.5032 13.4442 20.827 13.4442 17.9686C13.4442 15.1071 11.5933 13.434 8.51888 13.434H2.96631V22.5063H8.55595V22.5032Z" fill="white"/>
                            <path d="M20.2914 0.4198H36.0067V3.00041H23.2608V10.9213H34.2609V13.434H23.2608V22.4692H36.0067V25.0498H20.2914V0.4198Z" fill="white"/>
                            <path d="M39.282 0.4198H49.1666C53.4616 0.4198 56.3599 2.89546 56.3599 6.63056C56.3599 9.70198 54.404 11.8288 51.1565 12.4925V12.6314C53.6717 12.7364 55.2414 14.4094 55.2414 16.9561V21.2808C55.2414 23.2008 55.4515 23.9324 56.2889 25.0498H53.1804C52.4821 24.2473 52.272 23.4107 52.272 21.2808V17.5117C52.272 15.07 51.0484 13.9526 48.3942 13.9526H42.2483V25.0468H39.2789V0.4198H39.282ZM48.6414 11.3751C51.5397 11.3751 53.2855 9.80385 53.2855 7.18928C53.2855 4.57471 51.5397 3.0035 48.6414 3.0035H42.2514V11.3751H48.6414Z" fill="white"/>
                            <path d="M58.9678 12.8382C58.9678 5.16431 63.5068 0 70.283 0C75.6255 0 79.6423 3.21033 80.6188 8.30365H77.4763C76.812 4.63955 74.0898 2.37379 70.283 2.37379C65.3237 2.37379 62.1102 6.45462 62.1102 12.8413C62.1102 19.157 65.2866 23.1329 70.3881 23.1329C74.8932 23.1329 78.2117 19.9596 78.2117 15.6319V14.7953H73.1813V12.2487H80.9679V25.0529H78.1407V21.04H78.0016C76.3238 23.9015 73.3915 25.4697 69.8288 25.4697C63.3678 25.4697 58.9678 20.3764 58.9678 12.8382Z" fill="white"/>
                            <path d="M85.6121 0.4198H101.327V3.00041H88.5815V10.9213H99.5815V13.434H88.5815V22.4692H101.327V25.0498H85.6121V0.4198Z" fill="white"/>
                            <path d="M110.872 3.00041H103.503V0.4198H121.208V3.00041H113.838V25.0498H110.869V3.00041H110.872Z" fill="white"/>
                            <path d="M122.193 17.166H125.336C125.651 20.9351 128.061 23.0959 131.868 23.0959C135.431 23.0959 137.841 21.2129 137.841 18.3853C137.841 15.9776 136.617 14.9651 132.671 14.0236L130.017 13.397C125.129 12.2456 123.136 10.2916 123.136 6.90839C123.136 2.82756 126.628 0 131.621 0C136.753 0 140.211 2.96647 140.597 7.60602H137.455C137.21 4.32778 135.044 2.37379 131.621 2.37379C128.444 2.37379 126.278 4.11787 126.278 6.66453C126.278 8.89633 127.431 9.87486 131.099 10.7454L133.892 11.409C138.99 12.6314 140.98 14.5144 140.98 18.0736C140.98 22.4692 137.244 25.4697 131.831 25.4697C126.278 25.4697 122.648 22.3303 122.193 17.166Z" fill="white"/>
                            <path d="M269.845 0.419861H279.729C284.024 0.419861 286.923 2.89552 286.923 6.63062C286.923 9.70205 284.967 11.8289 281.719 12.4926V12.6315C284.234 12.7364 285.804 14.4095 285.804 16.9562V21.2809C285.804 23.2009 286.014 23.9325 286.852 25.0499H283.743C283.045 24.2473 282.835 23.4108 282.835 21.2809V17.5118C282.835 15.0701 281.611 13.9527 278.957 13.9527H272.811V25.0468H269.842V0.419861H269.845ZM279.204 11.3751C282.102 11.3751 283.848 9.80391 283.848 7.18934C283.848 4.57477 282.102 3.00356 279.204 3.00356H272.814V11.3751H279.204Z" fill="white"/>
                            <path d="M289.531 12.7364C289.531 5.09643 294.07 0.00311279 300.88 0.00311279C307.69 0.00311279 312.229 5.09643 312.229 12.7364C312.229 20.3764 307.69 25.4697 300.88 25.4697C294.07 25.4697 289.531 20.3764 289.531 12.7364ZM309.087 12.7364C309.087 6.45774 305.873 2.37382 300.88 2.37382C295.886 2.37382 292.673 6.45465 292.673 12.7364C292.673 19.0151 295.886 23.099 300.88 23.099C305.873 23.099 309.087 19.0151 309.087 12.7364Z" fill="white"/>
                            <path d="M314.593 1.09583H313.517V0.719238H316.104V1.09583H315.028V4.31543H314.596V1.09583H314.593Z" fill="white"/>
                            <path d="M316.601 0.719238H317.315L318.43 3.97279L319.546 0.719238H320.26V4.31543H319.83V1.11436L318.724 4.31234H318.134L317.028 1.11436V4.31234H316.598V0.719238H316.601Z" fill="white"/>
                        </svg>
                    </a>
                </div>
            <?php 
            endif; 
            ?>
        </div>
        <a class="vm-header__halfmoon" href="/">
            <svg width="45.6" height="21.12" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0.63 47.68 22.07">
                <path d="M23.839 0.630249C9.5899 0.630249 0 9.33891 0 22.705H4.99666H42.6787H43.9233H47.6754C47.678 9.33891 38.0881 0.630249 23.839 0.630249Z" fill="white"></path>
            </svg>
        </a>
        <div class="vm-col-5 vm-col-lg-7 vm-col-xl-5">
            <div class="vm-header__menu">
                <?php wp_nav_menu(array('theme_location' => 'main-menu')) ?>
            </div>
            <div class="vm-header__icons">
                <div class="vm-header__hamburger js-mobile-menu"></div>
            </div>
        </div>
        <div class="vm-col-1 vm-col-lg-1 vm-col-xl-1">
            <div class="vm-header__icons">
                <div class="vm-header__search js-search-btn"></div>
                <div class="js-search vm-header__search-form">
                    <?php echo do_shortcode('[wpdreams_ajaxsearchlite]'); ?>
                </div>
            </div>
        </div>
    </div>
    <div class="vm-header__menu vm-header__menu--mobile js-menu">
        <div class="vm-header__menu-logo">
            <div class="vm-grid">
                <div class="vm-col-6 vm-col-align-left">
                    <a href="/">
                        <svg width="193" height="23" viewBox="0 0 193 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M53.0886 1.83252H58.4122V2.7076H54.0932V5.55689H57.819V6.40825H54.0932V10.1801H53.0886V1.83252Z" fill="white"/>
                            <path d="M61.2046 1.83252H62.6732L65.8558 10.1827H64.7668L63.9626 8.01872H59.9152L59.111 10.1827H58.022L61.2046 1.83252ZM63.6541 7.16736L61.9376 2.50728L60.2211 7.16736H63.6541Z" fill="white"/>
                            <path d="M66.1697 7.50948H67.2349C67.3404 8.78784 68.1578 9.52059 69.4472 9.52059C70.6548 9.52059 71.4696 8.88272 71.4696 7.9233C71.4696 7.1062 71.0556 6.76355 69.7188 6.44462L68.8196 6.23112C67.1638 5.84102 66.4887 5.17944 66.4887 4.03023C66.4887 2.64644 67.6726 1.68701 69.3628 1.68701C71.1031 1.68701 72.2738 2.69125 72.403 4.26482H71.3377C71.2534 3.15251 70.5203 2.49093 69.3628 2.49093C68.287 2.49093 67.5513 3.08135 67.5513 3.94589C67.5513 4.70236 67.9416 5.03447 69.1835 5.32968L70.1301 5.55372C71.8572 5.96754 72.5322 6.6054 72.5322 7.81259C72.5322 9.30182 71.2665 10.3192 69.4314 10.3192C67.554 10.3245 66.3226 9.25964 66.1697 7.50948Z" fill="white"/>
                            <path d="M75.5671 2.7076H73.0701V1.83252H79.0687V2.7076H76.5717V10.1827H75.5671V2.7076Z" fill="white"/>
                            <path d="M80.1471 1.83252H81.1517V10.1827H80.1471V1.83252Z" fill="white"/>
                            <path d="M82.5994 6.04456C82.5994 3.44304 84.1366 1.69287 86.4332 1.69287C88.2447 1.69287 89.6052 2.78146 89.9348 4.5079H88.8696C88.6454 3.26644 87.7226 2.49679 86.4332 2.49679C84.7536 2.49679 83.6646 3.88058 83.6646 6.04456C83.6646 8.18482 84.7404 9.53435 86.4701 9.53435C87.9968 9.53435 89.1201 8.45895 89.1201 6.99081V6.70615H87.4167V5.84161H90.0561V10.1828H89.099V8.82269H89.0515C88.4846 9.79266 87.4906 10.3251 86.2829 10.3251C84.0891 10.3251 82.5994 8.59864 82.5994 6.04456Z" fill="white"/>
                            <path d="M91.7937 1.83252H92.8115V5.34603H97.0224V1.83252H98.0402V10.1827H97.0224V6.22112H92.8115V10.1827H91.7937V1.83252Z" fill="white"/>
                            <path d="M99.9387 1.83252H105.262V2.7076H100.943V5.39348H104.669V6.24484H100.943V9.30762H105.262V10.1827H99.9387V1.83252Z" fill="white"/>
                            <path d="M108.606 2.7076H106.109V1.83252H112.107V2.7076H109.61V10.1827H108.606V2.7076Z" fill="white"/>
                            <path d="M112.564 7.50948H113.629C113.734 8.78784 114.552 9.52059 115.841 9.52059C117.049 9.52059 117.863 8.88272 117.863 7.9233C117.863 7.1062 117.45 6.76355 116.113 6.44462L115.214 6.23112C113.558 5.84102 112.883 5.17944 112.883 4.03023C112.883 2.64644 114.067 1.68701 115.757 1.68701C117.497 1.68701 118.668 2.69125 118.797 4.26482H117.732C117.65 3.15251 116.914 2.49093 115.757 2.49093C114.681 2.49093 113.945 3.08135 113.945 3.94589C113.945 4.70236 114.335 5.03447 115.577 5.32968L116.524 5.55372C118.251 5.96754 118.926 6.6054 118.926 7.81259C118.926 9.30182 117.66 10.3192 115.825 10.3192C113.948 10.3245 112.719 9.25964 112.564 7.50948Z" fill="white"/>
                            <path d="M120.395 1.83252H125.718V2.7076H121.399V5.55689H125.125V6.40825H121.399V10.1801H120.395V1.83252Z" fill="white"/>
                            <path d="M126.296 6.00746C126.296 3.41647 127.833 1.69003 130.14 1.69003C132.447 1.69003 133.985 3.41647 133.985 6.00746C133.985 8.59844 132.447 10.3249 130.14 10.3249C127.833 10.3249 126.296 8.59844 126.296 6.00746ZM132.922 6.00746C132.922 3.87774 131.833 2.49395 130.14 2.49395C128.448 2.49395 127.359 3.87774 127.359 6.00746C127.359 8.13718 128.448 9.52097 130.14 9.52097C131.833 9.52097 132.922 8.13718 132.922 6.00746ZM128.603 0.000488281H129.668V1.12333H128.603V0.000488281ZM130.615 0.000488281H131.68V1.12333H130.615V0.000488281Z" fill="white"/>
                            <path d="M135.456 1.83252H138.805C140.26 1.83252 141.241 2.67334 141.241 3.93852C141.241 4.97966 140.579 5.70186 139.477 5.9259V5.97335C140.329 6.00762 140.861 6.57695 140.861 7.43885V8.90435C140.861 9.55539 140.933 9.80316 141.217 10.1827H140.165C139.928 9.91122 139.857 9.62656 139.857 8.90435V7.62599C139.857 6.79835 139.443 6.4188 138.544 6.4188H136.461V10.1801H135.456V1.83252ZM138.628 5.54635C139.609 5.54635 140.202 5.01392 140.202 4.1283C140.202 3.24003 139.611 2.71024 138.628 2.71024H136.463V5.54899H138.628V5.54635Z" fill="white"/>
                            <path d="M142.768 1.83252H144.424L147.016 9.38933L149.608 1.83252H151.263V10.1827H150.269V2.75505L147.701 10.1827H146.33L143.762 2.75505V10.1827H142.768V1.83252Z" fill="white"/>
                            <path d="M153.167 1.83252H158.493V2.7076H154.174V5.39348H157.9V6.24484H154.174V9.30762H158.493V10.1827H153.17V1.83252H153.167Z" fill="white"/>
                            <path d="M159.733 1.83252H162.491C164.835 1.83252 166.359 3.51152 166.359 6.00761C166.359 8.50371 164.832 10.1827 162.491 10.1827H159.733V1.83252ZM162.491 9.30762C164.218 9.30762 165.296 8.04244 165.296 6.00761C165.296 3.97278 164.22 2.7076 162.491 2.7076H160.74V9.30762H162.491Z" fill="white"/>
                            <path d="M167.806 1.83252H168.811V9.30762H173.13V10.1827H167.806V1.83252Z" fill="white"/>
                            <path d="M174.203 1.83252H175.211V10.1827H174.203V1.83252Z" fill="white"/>
                            <path d="M177.085 1.83252H178.575L182.551 9.18901V1.83252H183.545V10.1827H182.056L178.079 2.82621V10.1827H177.085V1.83252Z" fill="white"/>
                            <path d="M185.017 6.04456C185.017 3.44304 186.554 1.69287 188.851 1.69287C190.662 1.69287 192.023 2.78146 192.352 4.5079H191.287C191.063 3.26644 190.14 2.49679 188.851 2.49679C187.171 2.49679 186.082 3.88058 186.082 6.04456C186.082 8.18482 187.158 9.53435 188.887 9.53435C190.414 9.53435 191.537 8.45895 191.537 6.99081V6.70615H189.834V5.84161H192.474V10.1828H191.516V8.82269H191.469C190.902 9.79266 189.908 10.3251 188.7 10.3251C186.509 10.3251 185.017 8.59864 185.017 6.04456Z" fill="white"/>
                            <path d="M53.0886 14.3365H56.0101C57.4525 14.3365 58.3885 15.1299 58.3885 16.3714C58.3885 17.3071 57.8559 18.0267 56.9093 18.2402V18.2876C58.0457 18.4642 58.7076 19.2681 58.7076 20.3699C58.7076 21.7668 57.6555 22.6894 56.0576 22.6894H53.0886V14.3365ZM55.8678 17.8975C56.7907 17.8975 57.3338 17.402 57.3338 16.548C57.3338 15.6966 56.7907 15.1985 55.8678 15.1985H54.0932V17.8949H55.8678V17.8975ZM55.9864 21.8248C57.0385 21.8248 57.6423 21.2581 57.6423 20.2882C57.6423 19.3182 57.0148 18.7515 55.9732 18.7515H54.0906V21.8275H55.9864V21.8248Z" fill="white"/>
                            <path d="M60.1604 14.3365H65.4841V15.2116H61.165V17.8975H64.8908V18.7489H61.165V21.8117H65.4841V22.6867H60.1604V14.3365Z" fill="white"/>
                            <path d="M66.726 14.3365H70.0747C71.5302 14.3365 72.511 15.1774 72.511 16.4425C72.511 17.4837 71.8492 18.2059 70.747 18.4299V18.4774C71.5987 18.5116 72.1313 19.081 72.1313 19.9429V21.4084C72.1313 22.0594 72.2025 22.3072 72.4873 22.6867H71.4352C71.1979 22.4153 71.1267 22.1306 71.1267 21.4084V20.13C71.1267 19.3024 70.7128 18.9228 69.8136 18.9228H67.7306V22.6841H66.726V14.3365ZM69.898 18.0504C70.8789 18.0504 71.4721 17.518 71.4721 16.6323C71.4721 15.7467 70.8815 15.2143 69.898 15.2143H67.7332V18.053H69.898V18.0504Z" fill="white"/>
                            <path d="M73.5183 18.5486C73.5183 15.9471 75.0556 14.1969 77.3522 14.1969C79.1636 14.1969 80.5242 15.2855 80.8538 17.0119H79.7886C79.5644 15.7705 78.6416 15.0008 77.3522 15.0008C75.6726 15.0008 74.5836 16.3846 74.5836 18.5486C74.5836 20.6889 75.6594 22.0384 77.3891 22.0384C78.9158 22.0384 80.039 20.963 80.039 19.4948V19.2102H78.3357V18.3456H80.9725V22.6868H80.0153V21.3267H79.9678C79.4009 22.2967 78.4069 22.8291 77.1992 22.8291C75.0081 22.8291 73.5183 21.1027 73.5183 18.5486Z" fill="white"/>
                            <path d="M82.7101 14.3365H88.0337V15.2116H83.7147V17.8975H87.4405V18.7489H83.7147V21.8117H88.0337V22.6867H82.7101V14.3365Z" fill="white"/>
                            <path d="M91.3798 15.2116H88.8828V14.3365H94.8815V15.2116H92.3844V22.6867H91.3798V15.2116Z" fill="white"/>
                            <path d="M95.3375 20.0144H96.4028C96.5083 21.2927 97.3257 22.0255 98.615 22.0255C99.8227 22.0255 100.637 21.3876 100.637 20.4282C100.637 19.6111 100.223 19.2684 98.8866 18.9495L97.9875 18.736C96.3316 18.3459 95.6566 17.6843 95.6566 16.5351C95.6566 15.1513 96.8405 14.1919 98.5306 14.1919C100.271 14.1919 101.442 15.1961 101.571 16.7697H100.506C100.424 15.6574 99.6882 14.9958 98.5306 14.9958C97.4548 14.9958 96.7218 15.5862 96.7218 16.4508C96.7218 17.2072 97.1121 17.5394 98.354 17.8346L99.3006 18.0586C101.028 18.4724 101.703 19.1103 101.703 20.3175C101.703 21.8067 100.437 22.8241 98.6018 22.8241C96.7218 22.8294 95.4905 21.7645 95.3375 20.0144Z" fill="white"/>
                            <path d="M103.169 14.3365H106.517C107.973 14.3365 108.954 15.1774 108.954 16.4425C108.954 17.4837 108.292 18.2059 107.19 18.4299V18.4774C108.042 18.5116 108.574 19.081 108.574 19.9429V21.4084C108.574 22.0594 108.645 22.3072 108.93 22.6867H107.878C107.641 22.4153 107.57 22.1306 107.57 21.4084V20.13C107.57 19.3024 107.156 18.9228 106.256 18.9228H104.173V22.6841H103.166V14.3365H103.169ZM106.338 18.0504C107.319 18.0504 107.912 17.518 107.912 16.6323C107.912 15.7467 107.322 15.2143 106.338 15.2143H104.173V18.053H106.338V18.0504Z" fill="white"/>
                            <path d="M109.961 18.5114C109.961 15.9204 111.498 14.194 113.805 14.194C116.113 14.194 117.65 15.9204 117.65 18.5114C117.65 21.1024 116.113 22.8288 113.805 22.8288C111.498 22.8288 109.961 21.1024 109.961 18.5114ZM116.585 18.5114C116.585 16.3817 115.496 14.9979 113.803 14.9979C112.11 14.9979 111.021 16.3817 111.021 18.5114C111.021 20.6411 112.11 22.0249 113.803 22.0249C115.498 22.0249 116.585 20.6411 116.585 18.5114Z" fill="white"/>
                            <path d="M118.612 21.5033H119.725V22.6868H118.612V21.5033Z" fill="white"/>
                            <path d="M120.827 20.0144H121.892C121.998 21.2927 122.815 22.0255 124.105 22.0255C125.312 22.0255 126.127 21.3876 126.127 20.4282C126.127 19.6111 125.713 19.2684 124.376 18.9495L123.477 18.736C121.821 18.3459 121.146 17.6843 121.146 16.5351C121.146 15.1513 122.33 14.1919 124.02 14.1919C125.761 14.1919 126.931 15.1961 127.061 16.7697H125.995C125.913 15.6574 125.178 14.9958 124.02 14.9958C122.945 14.9958 122.209 15.5862 122.209 16.4508C122.209 17.2072 122.599 17.5394 123.841 17.8346L124.788 18.0586C126.515 18.4724 127.19 19.1103 127.19 20.3175C127.19 21.8067 125.924 22.8241 124.089 22.8241C122.211 22.8294 120.983 21.7645 120.827 20.0144Z" fill="white"/>
                            <path d="M128.658 14.3365H133.982V15.2116H129.663V17.8975H133.389V18.7489H129.663V21.8117H133.982V22.6867H128.658V14.3365Z" fill="white"/>
                            <path d="M23.839 0.630249C9.5899 0.630249 0 9.33891 0 22.705H4.99666H42.6787H43.9233H47.6754C47.678 9.33891 38.0881 0.630249 23.839 0.630249Z" fill="white"/>
                        </svg>
                    </a>
                </div>
                <div class="vm-col-6 vm-col-align-right">
                    <div class="vm-header__menu-close js-menu-close">
                        ✕
                    </div>
                </div>
            </div>
        </div>
        <?php wp_nav_menu(array('theme_location' => 'main-menu')) ?>
    </div>

    <?php 
    if( is_singular('houses') ) { 

        // get fresh house data from API
        if (get_field('house_id') && get_field('property_type')) {
            update_estate_json(get_field('house_id'), get_field('property_type'), get_the_ID());
        }
        $house_details_json = get_field('json_data');
        $house_details = ($house_details_json) ? json_decode($house_details_json, true) : [];
        $property_type = get_field('property_type');
        $images_array = (isset($house_details['images'])) ? $house_details['images'] : false;

        // decide if we should sync the data
        $update_running = get_option('vitec_update_running', false);
        $last_sync = get_post_meta(get_the_ID(), '_last_gallery_sync', true);
        $should_sync = false;
        if ($last_sync && (time() - $last_sync) < 15) { // less than 15 seconds since last sync
            $should_sync = true;
        }
        
        // manipulate gallery data according to fresh data from the API
        $gallery_needs_update = false;
        $gallery_data = get_field('gallery_data');
        
        // don't run this code when the update is running
        if (!$update_running && $should_sync) {
            if ($images_array && is_array($gallery_data) && !empty($gallery_data)) {
                foreach($gallery_data as $key => $image) {

                    // find the image in the json_data array
                    $vitec_image_id = $image['api_id'];
                    if(!$vitec_image_id) unset($gallery_data[$key]);
                    $image = array_filter($images_array, function($img) use ($vitec_image_id) {
                        return $img['imageId'] == $vitec_image_id;
                    });
                    if(empty($image)) unset($gallery_data[$key]);
                    $image = array_shift($image);

                    // update data
                    $gallery_data[$key]['order'] = $image['orderNumber'];
                    $gallery_data[$key]['show'] = $image['showImageOnInternet'];
                    $gallery_data[$key]['category'] = $image['category'];
                }

                // update data in the database
                $gallery_needs_update = true;
            }
        }

        if ($gallery_needs_update) {
            update_field('gallery_data', $gallery_data);
            update_post_meta(get_the_ID(), '_last_gallery_sync', time());
        }

        // manipulate plans data according to fresh data from the API
        $plans_needs_update = false;
        $plans_data = get_field('plans_data');

        // don't run this code when the update is running
        if (!$update_running && $should_sync) {
            if ($images_array && is_array($plans_data) && !empty($plans_data)) {
                foreach($plans_data as $key => $plan) {

                    // find the image in the json_data array
                    $vitec_plan_id = $plan['api_id'];
                    if(!$vitec_plan_id) unset($plans_data[$key]);
                    $plan = array_filter($images_array, function($img) use ($vitec_plan_id) {
                        return $img['imageId'] == $vitec_plan_id;
                    });
                    if(empty($plan)) unset($plans_data[$key]);
                    $plan = array_shift($plan);

                    // update data
                    $plans_data[$key]['order'] = $plan['orderNumber'];
                    $plans_data[$key]['show'] = $plan['showImageOnInternet'];
                }

                // update data in the database
                $plans_needs_update = true;
            }
        }

        if ($plans_needs_update) {
            update_field('plans_data', $plans_data);
            update_post_meta(get_the_ID(), '_last_gallery_sync', time());
        }
        
        // save data as global variables to be accessible on single.php page
        $GLOBALS['house_details'] = $house_details;
        $GLOBALS['gallery_data'] = $gallery_data;
        $GLOBALS['plans_data'] = $plans_data;

        ?>
        <div class="vm-grid vm-grid--alt vm-grid--align-center">
            <div class="vm-col-12 vm-col-xl-2">
                <div class="vm-header__logo vm-header__logo--alt" data-aos="fade-right">
                    <a href="/">
                        <svg width="193" height="23" viewBox="0 0 193 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M53.0886 1.83252H58.4122V2.7076H54.0932V5.55689H57.819V6.40825H54.0932V10.1801H53.0886V1.83252Z" fill="white"/>
                            <path d="M61.2046 1.83252H62.6732L65.8558 10.1827H64.7668L63.9626 8.01872H59.9152L59.111 10.1827H58.022L61.2046 1.83252ZM63.6541 7.16736L61.9376 2.50728L60.2211 7.16736H63.6541Z" fill="white"/>
                            <path d="M66.1697 7.50948H67.2349C67.3404 8.78784 68.1578 9.52059 69.4472 9.52059C70.6548 9.52059 71.4696 8.88272 71.4696 7.9233C71.4696 7.1062 71.0556 6.76355 69.7188 6.44462L68.8196 6.23112C67.1638 5.84102 66.4887 5.17944 66.4887 4.03023C66.4887 2.64644 67.6726 1.68701 69.3628 1.68701C71.1031 1.68701 72.2738 2.69125 72.403 4.26482H71.3377C71.2534 3.15251 70.5203 2.49093 69.3628 2.49093C68.287 2.49093 67.5513 3.08135 67.5513 3.94589C67.5513 4.70236 67.9416 5.03447 69.1835 5.32968L70.1301 5.55372C71.8572 5.96754 72.5322 6.6054 72.5322 7.81259C72.5322 9.30182 71.2665 10.3192 69.4314 10.3192C67.554 10.3245 66.3226 9.25964 66.1697 7.50948Z" fill="white"/>
                            <path d="M75.5671 2.7076H73.0701V1.83252H79.0687V2.7076H76.5717V10.1827H75.5671V2.7076Z" fill="white"/>
                            <path d="M80.1471 1.83252H81.1517V10.1827H80.1471V1.83252Z" fill="white"/>
                            <path d="M82.5994 6.04456C82.5994 3.44304 84.1366 1.69287 86.4332 1.69287C88.2447 1.69287 89.6052 2.78146 89.9348 4.5079H88.8696C88.6454 3.26644 87.7226 2.49679 86.4332 2.49679C84.7536 2.49679 83.6646 3.88058 83.6646 6.04456C83.6646 8.18482 84.7404 9.53435 86.4701 9.53435C87.9968 9.53435 89.1201 8.45895 89.1201 6.99081V6.70615H87.4167V5.84161H90.0561V10.1828H89.099V8.82269H89.0515C88.4846 9.79266 87.4906 10.3251 86.2829 10.3251C84.0891 10.3251 82.5994 8.59864 82.5994 6.04456Z" fill="white"/>
                            <path d="M91.7937 1.83252H92.8115V5.34603H97.0224V1.83252H98.0402V10.1827H97.0224V6.22112H92.8115V10.1827H91.7937V1.83252Z" fill="white"/>
                            <path d="M99.9387 1.83252H105.262V2.7076H100.943V5.39348H104.669V6.24484H100.943V9.30762H105.262V10.1827H99.9387V1.83252Z" fill="white"/>
                            <path d="M108.606 2.7076H106.109V1.83252H112.107V2.7076H109.61V10.1827H108.606V2.7076Z" fill="white"/>
                            <path d="M112.564 7.50948H113.629C113.734 8.78784 114.552 9.52059 115.841 9.52059C117.049 9.52059 117.863 8.88272 117.863 7.9233C117.863 7.1062 117.45 6.76355 116.113 6.44462L115.214 6.23112C113.558 5.84102 112.883 5.17944 112.883 4.03023C112.883 2.64644 114.067 1.68701 115.757 1.68701C117.497 1.68701 118.668 2.69125 118.797 4.26482H117.732C117.65 3.15251 116.914 2.49093 115.757 2.49093C114.681 2.49093 113.945 3.08135 113.945 3.94589C113.945 4.70236 114.335 5.03447 115.577 5.32968L116.524 5.55372C118.251 5.96754 118.926 6.6054 118.926 7.81259C118.926 9.30182 117.66 10.3192 115.825 10.3192C113.948 10.3245 112.719 9.25964 112.564 7.50948Z" fill="white"/>
                            <path d="M120.395 1.83252H125.718V2.7076H121.399V5.55689H125.125V6.40825H121.399V10.1801H120.395V1.83252Z" fill="white"/>
                            <path d="M126.296 6.00746C126.296 3.41647 127.833 1.69003 130.14 1.69003C132.447 1.69003 133.985 3.41647 133.985 6.00746C133.985 8.59844 132.447 10.3249 130.14 10.3249C127.833 10.3249 126.296 8.59844 126.296 6.00746ZM132.922 6.00746C132.922 3.87774 131.833 2.49395 130.14 2.49395C128.448 2.49395 127.359 3.87774 127.359 6.00746C127.359 8.13718 128.448 9.52097 130.14 9.52097C131.833 9.52097 132.922 8.13718 132.922 6.00746ZM128.603 0.000488281H129.668V1.12333H128.603V0.000488281ZM130.615 0.000488281H131.68V1.12333H130.615V0.000488281Z" fill="white"/>
                            <path d="M135.456 1.83252H138.805C140.26 1.83252 141.241 2.67334 141.241 3.93852C141.241 4.97966 140.579 5.70186 139.477 5.9259V5.97335C140.329 6.00762 140.861 6.57695 140.861 7.43885V8.90435C140.861 9.55539 140.933 9.80316 141.217 10.1827H140.165C139.928 9.91122 139.857 9.62656 139.857 8.90435V7.62599C139.857 6.79835 139.443 6.4188 138.544 6.4188H136.461V10.1801H135.456V1.83252ZM138.628 5.54635C139.609 5.54635 140.202 5.01392 140.202 4.1283C140.202 3.24003 139.611 2.71024 138.628 2.71024H136.463V5.54899H138.628V5.54635Z" fill="white"/>
                            <path d="M142.768 1.83252H144.424L147.016 9.38933L149.608 1.83252H151.263V10.1827H150.269V2.75505L147.701 10.1827H146.33L143.762 2.75505V10.1827H142.768V1.83252Z" fill="white"/>
                            <path d="M153.167 1.83252H158.493V2.7076H154.174V5.39348H157.9V6.24484H154.174V9.30762H158.493V10.1827H153.17V1.83252H153.167Z" fill="white"/>
                            <path d="M159.733 1.83252H162.491C164.835 1.83252 166.359 3.51152 166.359 6.00761C166.359 8.50371 164.832 10.1827 162.491 10.1827H159.733V1.83252ZM162.491 9.30762C164.218 9.30762 165.296 8.04244 165.296 6.00761C165.296 3.97278 164.22 2.7076 162.491 2.7076H160.74V9.30762H162.491Z" fill="white"/>
                            <path d="M167.806 1.83252H168.811V9.30762H173.13V10.1827H167.806V1.83252Z" fill="white"/>
                            <path d="M174.203 1.83252H175.211V10.1827H174.203V1.83252Z" fill="white"/>
                            <path d="M177.085 1.83252H178.575L182.551 9.18901V1.83252H183.545V10.1827H182.056L178.079 2.82621V10.1827H177.085V1.83252Z" fill="white"/>
                            <path d="M185.017 6.04456C185.017 3.44304 186.554 1.69287 188.851 1.69287C190.662 1.69287 192.023 2.78146 192.352 4.5079H191.287C191.063 3.26644 190.14 2.49679 188.851 2.49679C187.171 2.49679 186.082 3.88058 186.082 6.04456C186.082 8.18482 187.158 9.53435 188.887 9.53435C190.414 9.53435 191.537 8.45895 191.537 6.99081V6.70615H189.834V5.84161H192.474V10.1828H191.516V8.82269H191.469C190.902 9.79266 189.908 10.3251 188.7 10.3251C186.509 10.3251 185.017 8.59864 185.017 6.04456Z" fill="white"/>
                            <path d="M53.0886 14.3365H56.0101C57.4525 14.3365 58.3885 15.1299 58.3885 16.3714C58.3885 17.3071 57.8559 18.0267 56.9093 18.2402V18.2876C58.0457 18.4642 58.7076 19.2681 58.7076 20.3699C58.7076 21.7668 57.6555 22.6894 56.0576 22.6894H53.0886V14.3365ZM55.8678 17.8975C56.7907 17.8975 57.3338 17.402 57.3338 16.548C57.3338 15.6966 56.7907 15.1985 55.8678 15.1985H54.0932V17.8949H55.8678V17.8975ZM55.9864 21.8248C57.0385 21.8248 57.6423 21.2581 57.6423 20.2882C57.6423 19.3182 57.0148 18.7515 55.9732 18.7515H54.0906V21.8275H55.9864V21.8248Z" fill="white"/>
                            <path d="M60.1604 14.3365H65.4841V15.2116H61.165V17.8975H64.8908V18.7489H61.165V21.8117H65.4841V22.6867H60.1604V14.3365Z" fill="white"/>
                            <path d="M66.726 14.3365H70.0747C71.5302 14.3365 72.511 15.1774 72.511 16.4425C72.511 17.4837 71.8492 18.2059 70.747 18.4299V18.4774C71.5987 18.5116 72.1313 19.081 72.1313 19.9429V21.4084C72.1313 22.0594 72.2025 22.3072 72.4873 22.6867H71.4352C71.1979 22.4153 71.1267 22.1306 71.1267 21.4084V20.13C71.1267 19.3024 70.7128 18.9228 69.8136 18.9228H67.7306V22.6841H66.726V14.3365ZM69.898 18.0504C70.8789 18.0504 71.4721 17.518 71.4721 16.6323C71.4721 15.7467 70.8815 15.2143 69.898 15.2143H67.7332V18.053H69.898V18.0504Z" fill="white"/>
                            <path d="M73.5183 18.5486C73.5183 15.9471 75.0556 14.1969 77.3522 14.1969C79.1636 14.1969 80.5242 15.2855 80.8538 17.0119H79.7886C79.5644 15.7705 78.6416 15.0008 77.3522 15.0008C75.6726 15.0008 74.5836 16.3846 74.5836 18.5486C74.5836 20.6889 75.6594 22.0384 77.3891 22.0384C78.9158 22.0384 80.039 20.963 80.039 19.4948V19.2102H78.3357V18.3456H80.9725V22.6868H80.0153V21.3267H79.9678C79.4009 22.2967 78.4069 22.8291 77.1992 22.8291C75.0081 22.8291 73.5183 21.1027 73.5183 18.5486Z" fill="white"/>
                            <path d="M82.7101 14.3365H88.0337V15.2116H83.7147V17.8975H87.4405V18.7489H83.7147V21.8117H88.0337V22.6867H82.7101V14.3365Z" fill="white"/>
                            <path d="M91.3798 15.2116H88.8828V14.3365H94.8815V15.2116H92.3844V22.6867H91.3798V15.2116Z" fill="white"/>
                            <path d="M95.3375 20.0144H96.4028C96.5083 21.2927 97.3257 22.0255 98.615 22.0255C99.8227 22.0255 100.637 21.3876 100.637 20.4282C100.637 19.6111 100.223 19.2684 98.8866 18.9495L97.9875 18.736C96.3316 18.3459 95.6566 17.6843 95.6566 16.5351C95.6566 15.1513 96.8405 14.1919 98.5306 14.1919C100.271 14.1919 101.442 15.1961 101.571 16.7697H100.506C100.424 15.6574 99.6882 14.9958 98.5306 14.9958C97.4548 14.9958 96.7218 15.5862 96.7218 16.4508C96.7218 17.2072 97.1121 17.5394 98.354 17.8346L99.3006 18.0586C101.028 18.4724 101.703 19.1103 101.703 20.3175C101.703 21.8067 100.437 22.8241 98.6018 22.8241C96.7218 22.8294 95.4905 21.7645 95.3375 20.0144Z" fill="white"/>
                            <path d="M103.169 14.3365H106.517C107.973 14.3365 108.954 15.1774 108.954 16.4425C108.954 17.4837 108.292 18.2059 107.19 18.4299V18.4774C108.042 18.5116 108.574 19.081 108.574 19.9429V21.4084C108.574 22.0594 108.645 22.3072 108.93 22.6867H107.878C107.641 22.4153 107.57 22.1306 107.57 21.4084V20.13C107.57 19.3024 107.156 18.9228 106.256 18.9228H104.173V22.6841H103.166V14.3365H103.169ZM106.338 18.0504C107.319 18.0504 107.912 17.518 107.912 16.6323C107.912 15.7467 107.322 15.2143 106.338 15.2143H104.173V18.053H106.338V18.0504Z" fill="white"/>
                            <path d="M109.961 18.5114C109.961 15.9204 111.498 14.194 113.805 14.194C116.113 14.194 117.65 15.9204 117.65 18.5114C117.65 21.1024 116.113 22.8288 113.805 22.8288C111.498 22.8288 109.961 21.1024 109.961 18.5114ZM116.585 18.5114C116.585 16.3817 115.496 14.9979 113.803 14.9979C112.11 14.9979 111.021 16.3817 111.021 18.5114C111.021 20.6411 112.11 22.0249 113.803 22.0249C115.498 22.0249 116.585 20.6411 116.585 18.5114Z" fill="white"/>
                            <path d="M118.612 21.5033H119.725V22.6868H118.612V21.5033Z" fill="white"/>
                            <path d="M120.827 20.0144H121.892C121.998 21.2927 122.815 22.0255 124.105 22.0255C125.312 22.0255 126.127 21.3876 126.127 20.4282C126.127 19.6111 125.713 19.2684 124.376 18.9495L123.477 18.736C121.821 18.3459 121.146 17.6843 121.146 16.5351C121.146 15.1513 122.33 14.1919 124.02 14.1919C125.761 14.1919 126.931 15.1961 127.061 16.7697H125.995C125.913 15.6574 125.178 14.9958 124.02 14.9958C122.945 14.9958 122.209 15.5862 122.209 16.4508C122.209 17.2072 122.599 17.5394 123.841 17.8346L124.788 18.0586C126.515 18.4724 127.19 19.1103 127.19 20.3175C127.19 21.8067 125.924 22.8241 124.089 22.8241C122.211 22.8294 120.983 21.7645 120.827 20.0144Z" fill="white"/>
                            <path d="M128.658 14.3365H133.982V15.2116H129.663V17.8975H133.389V18.7489H129.663V21.8117H133.982V22.6867H128.658V14.3365Z" fill="white"/>
                            <path d="M23.839 0.630249C9.5899 0.630249 0 9.33891 0 22.705H4.99666H42.6787H43.9233H47.6754C47.678 9.33891 38.0881 0.630249 23.839 0.630249Z" fill="white"/>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="vm-col-12 vm-col-xl-10">
                <div class="vm-header__menu">
                    <div class="menu-main-menu-container">
                        <ul id="menu-main-menu-2" class="menu">
                            <li class="menu-item mobile"><a class="js-tablink-menu-mobile" href="#tabs"><?php echo __('Bostadsinfo', TEXTDOMAIN ); ?></a></li>
                            <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab1" data-tabid="tab1"><?php echo __('Fakta', TEXTDOMAIN ); ?></a></li>
                            <?php if ((isset($house_details['interior']['generealDescription']) && !empty($house_details['interior']['generealDescription'])) || (isset($house_details['houseInterior']['generealDescription']) && !empty($house_details['houseInterior']['generealDescription']))): ?>
                                <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab2" data-tabid="tab2"><?php echo __('Rumsbeskrivning', TEXTDOMAIN ); ?></a></li>
                            <?php endif; ?>
                            <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab3" data-tabid="tab3"><?php echo __('Byggnad', TEXTDOMAIN ); ?></a></li>
                            <?php 
                            if ($property_type !== 'houses') : 
                            ?>
                                <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab4" data-tabid="tab4"><?php echo __('Förening', TEXTDOMAIN ); ?></a></li>
                            <?php endif; ?>
                            <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab5" data-tabid="tab5"><?php echo __('Ekonomi', TEXTDOMAIN ); ?></a></li>
                            <?php if (is_array(get_field('documents'))): ?>
                                <li class="menu-item desktop"><a class="js-tablink-menu" href="#tab6" data-tabid="tab6"><?php echo __('Dokument', TEXTDOMAIN ); ?></a></li>
                            <?php endif; ?>
                            <?php if ($gallery_data && is_array($gallery_data) && !empty($gallery_data)): ?>
                                <li class="menu-item menu-item-btn"><button id="show-more-btn" class="vm-button vm-button--dark" data-expanded="false"><span class="desktop-only"><?php echo __('Alla', TEXTDOMAIN ); ?></span> <?php echo __('bilder', TEXTDOMAIN ); ?></button></li>
                            <?php endif; ?>
                            <?php if ($plans_data && is_array($plans_data) && !empty($plans_data)): ?>
                                <li class="menu-item menu-item-btn"><button id="show-plans-btn" class="vm-button vm-button--dark" data-expanded="false"><?php echo __('Planritning', TEXTDOMAIN ); ?></button></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php } ?>
</header>